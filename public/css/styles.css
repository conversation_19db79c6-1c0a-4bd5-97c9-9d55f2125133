/* Reset e configurações básicas */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Robot<PERSON>,
    sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 20px;
}

/* Container principal */
.container {
  max-width: 1600px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
}

.header p {
  font-size: 1.1em;
  opacity: 0.9;
}

/* Layout principal */
.main-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 0;
}

.form-container {
  padding: 30px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  max-height: 800px;
  overflow-y: auto;
}

.result-container {
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
}

/* Cabeçalho do formulário */
.form-header {
  margin-bottom: 30px;
}

.form-header h2 {
  color: #495057;
  font-size: 1.8em;
  margin-bottom: 8px;
}

.form-header p {
  color: #6c757d;
  font-size: 1.1em;
}

/* Formulário */
.pix-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  font-size: 0.9em;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.char-counter,
.form-group small {
  font-size: 0.8em;
  color: #6c757d;
  margin-top: 4px;
}

/* Mensagens de validação */
.validation-message {
  font-size: 0.8em;
  margin-top: 4px;
}

.validation-message.success {
  color: #28a745;
}

.validation-message.error {
  color: #dc3545;
}

/* Toggle Switch */
.customization-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 30px 0 20px 0;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.toggle-switch {
  position: relative;
  width: 50px;
  height: 24px;
  background: #ccc;
  border-radius: 12px;
  cursor: pointer;
  transition: background 0.3s;
}

.toggle-switch.active {
  background: #667eea;
}

.toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s;
}

.toggle-switch.active .toggle-slider {
  transform: translateX(26px);
}

.toggle-label {
  font-weight: 600;
  color: #495057;
}

.toggle-description {
  font-size: 0.9em;
  color: #6c757d;
}

/* Painel de personalização */
.customization-panel {
  display: none;
  background: white;
  border-radius: 12px;
  border: 2px solid #e9ecef;
  padding: 20px;
  margin-top: 20px;
}

.customization-panel.active {
  display: block;
}

.section-title {
  font-size: 1.1em;
  font-weight: 700;
  color: #495057;
  margin: 20px 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e9ecef;
}

.section-title:first-child {
  margin-top: 0;
}

.color-input {
  height: 45px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.form-control-small {
  padding: 8px;
  font-size: 13px;
}

/* Botões */
.generate-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 30px;
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.generate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  display: none;
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Área de resultado */
.qr-placeholder {
  text-align: center;
  color: #6c757d;
  padding: 60px 20px;
}

.qr-placeholder .qr-icon {
  margin-bottom: 20px;
}

.qr-result {
  text-align: center;
  display: none;
}

.qr-image-container {
  position: relative;
  margin-bottom: 20px;
}

#qr-preview {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 10px;
  background: white;
  min-height: 300px;
  min-width: 300px;
  max-height: 400px;
  max-width: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

#qr-preview.margin-none {
  padding: 1px;
  border: 2px solid #e9ecef; /* Neutral border */
  overflow: hidden; /* Ensure cropping works properly */
}

#qr-preview.margin-none canvas,
#qr-preview.margin-none svg {
  /* Optimize rendering for zero-margin QR codes */
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-crisp-edges;
  image-rendering: pixelated;
  image-rendering: crisp-edges;

  /* Ensure pixel-perfect alignment */
  transform-origin: center center;

  /* Prevent blurry rendering on high-DPI displays */
  -webkit-font-smoothing: none;
  -moz-osx-font-smoothing: grayscale;
}

#qr-preview.margin-default {
  padding: 10px;
  border: 2px solid #e9ecef; /* Neutral border */
}

#qr-preview.margin-wide {
  padding: 20px;
  border: 2px solid #e9ecef; /* Neutral border */
}

#qrImage {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  border-radius: 8px;
  object-fit: contain;
}

.download-btn,
.copy-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin: 5px;
  transition: all 0.3s ease;
}

.download-btn:hover,
.copy-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.qr-info h3 {
  color: #28a745;
  margin-bottom: 15px;
}

.br-code-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
}

.br-code-container label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  display: block;
}

.br-code-text {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 0.9em;
  word-break: break-all;
  margin-bottom: 10px;
}

.pix-details {
  margin-top: 20px;
}

.pix-detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.pix-detail-label {
  font-weight: 600;
  color: #495057;
}

.pix-detail-value {
  color: #6c757d;
  text-align: right;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 15% auto;
  padding: 0;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  padding: 20px;
  border-radius: 12px 12px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 20px;
}

/* Toast */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #28a745;
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  font-weight: 600;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.toast.show {
  transform: translateX(0);
}

.toast.error {
  background: #dc3545;
}

/* Upload de imagem */
.image-upload {
  position: relative;
}

.image-upload input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.image-upload-label {
  display: block;
  padding: 12px;
  border: 2px dashed #e9ecef;
  border-radius: 8px;
  text-align: center;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-upload-label:hover {
  border-color: #667eea;
  color: #667eea;
}

/* Botões de preset */
.preset-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-top: 15px;
}

.preset-btn {
  padding: 10px 15px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  color: #495057;
  font-size: 0.9em;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.preset-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: #f8f9ff;
}

/* Range inputs */
input[type="range"] {
  width: 100%;
  margin: 10px 0;
}

.range-value {
  font-size: 0.9em;
  color: #6c757d;
  text-align: center;
}

/* Grupo de botões de margem - Design compacto inspirado em interfaces modernas */
.margin-button-group {
  display: flex;
  gap: 3px;
  margin-top: 8px;
}

.margin-btn {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  color: #6c757d;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  min-height: 36px;
  max-width: 80px;
}

.margin-btn:hover:not(.active) {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.margin-btn.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.margin-btn .margin-label {
  font-weight: 600;
  font-size: 13px;
  line-height: 1.2;
}

.margin-btn .margin-value {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.1;
}

/* Media queries responsivas */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
  }

  .form-container {
    max-height: none;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  #qr-preview {
    max-width: 350px;
    max-height: 350px;
  }
}

@media (max-width: 768px) {
  .container {
    margin: 10px;
    border-radius: 15px;
  }

  .header {
    padding: 20px;
  }

  .header h1 {
    font-size: 2em;
  }

  .form-container,
  .result-container {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  #qr-preview {
    max-width: 300px;
    max-height: 300px;
    min-width: 250px;
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  #qr-preview {
    max-width: 250px;
    max-height: 250px;
    min-width: 200px;
    min-height: 200px;
    padding: 10px;
  }

  #qr-preview.margin-none {
    padding: 3px;
  }

  #qr-preview.margin-default {
    padding: 8px;
  }

  #qr-preview.margin-wide {
    padding: 12px;
  }

  /* Compact margin buttons on mobile */
  .margin-btn {
    padding: 4px 8px;
    min-height: 32px;
    max-width: 70px;
  }

  .margin-btn .margin-label {
    font-size: 12px;
  }

  .margin-btn .margin-value {
    font-size: 11px;
  }
}
