{"name": "qr-border-plugin", "private": false, "version": "0.1.1", "description": "Plugin for generating different borders of qr-code-styling", "author": "<PERSON><PERSON>", "main": "./build/index.js", "types": "./build/index.d.ts", "publishConfig": {"access": "public"}, "files": ["build"], "dependencies": {"@lefe-dev/lefe-verify-license": "^0.1.1"}, "devDependencies": {"@types/jsdom": "^21.1.7", "jsdom": "^25.0.1", "qr-code-styling": "^1.6.0", "ts-loader": "^9.5.1", "webpack": "^5.94.0", "webpack-cli": "^5.1.4"}, "peerDependencies": {"qr-code-styling": "^1.6.0"}, "scripts": {"build": "webpack --config webpack.config.js", "test": "jest", "lint": "eslint .", "format": "prettier . --write"}, "repository": {"type": "git", "url": "git+https://github.com/kozakdenys/qr-plugins-public.git"}, "keywords": ["qr", "code", "qr-code-styling", "plugin", "extension", "frame", "border"], "license": "SEE LICENSE IN LICENSE.md", "bugs": {"url": "https://github.com/kozakdenys/qr-plugins-public/issues"}, "homepage": "https://lefe.dev/marketplace/qr-border-plugin"}