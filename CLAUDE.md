# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Build production**: `npm run build` - Creates optimized production build in `lib/` directory
- **Build development**: `npm run build:dev` - Creates development build with source maps
- **Run tests**: `npm test` - Executes Jest test suite with coverage collection
- **Start dev server**: `npm start` - Starts webpack dev server with live reload for testing/demo
- **Lint code**: Use ESLint via `npx eslint src/` - Project uses ESLint 9 with TypeScript support

## Project Architecture

This is a TypeScript library for generating customizable QR codes with styling options and logo support. The codebase is structured as follows:

### Core Architecture
- **Entry point**: `src/index.ts` - Main library export
- **Main class**: `src/core/QRCodeStyling.ts` - Primary API class that users interact with
- **SVG rendering**: `src/core/QRSVG.ts` - Handles SVG-based QR code generation
- **Configuration**: `src/core/QROptions.ts` - Default options and option validation

### Key Directories
- `src/core/` - Core QR code generation and rendering logic
- `src/tools/` - Utility functions (image handling, data conversion, sanitization)
- `src/constants/` - Type definitions and constant values for styling options
- `src/types/` - TypeScript type definitions
- `src/figures/` - Shape rendering components for different QR code elements

### Build System
- **TypeScript**: Compiles to ES2017, outputs to `lib/` directory with declarations
- **Webpack**: Multiple configs for different build targets (development, production, dev-server)
- **Testing**: Jest with ts-jest preset, uses jsdom environment for DOM testing
- **Coverage**: Collects from all TypeScript files in `src/` excluding `.d.ts`

### Key Features
- Supports both Canvas and SVG rendering
- Node.js compatibility with node-canvas and jsdom
- Extensible through plugin system (`applyExtension` method)
- Multiple export formats (PNG, JPEG, WebP, SVG)
- Comprehensive styling options (dots, corners, gradients, backgrounds)

### Testing
- Uses Jest with `jest-fixed-jsdom` environment
- Test files are co-located with source files (`.test.js` extension)
- Coverage collection enabled by default

### Development Notes
- Entry point for library consumers is `lib/qr-code-styling.js`
- Node.js usage requires passing `nodeCanvas` and/or `jsdom` objects to constructor
- The library depends on `qrcode-generator` for core QR generation logic