name: Create Release PR
on:
  workflow_dispatch:
    inputs:
      semver:
        description: "New Version(semver)"
        required: true
        default: "patch"
        type: choice
        options:
          - patch
          - minor
          - major
permissions:
  contents: write
  pull-requests: write

jobs:
  create-release-pr:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v3
      - name: Update Version
        run: |
          git config --global user.email "${GIT_AUTHOR_EMAIL}"
          git config --global user.name "${GIT_AUTHOR_NAME}"
          npm run ci:versionup:${SEMVER} --yes
        env:
          SEMVER: ${{ github.event.inputs.semver }}
          GIT_AUTHOR_NAME: ${{ github.actor }}
          GIT_AUTHOR_EMAIL: ${{ github.actor }}@users.noreply.github.com
      - name: Set PACKAGE_VERSION
        run: echo "PACKAGE_VERSION=$(cat package.json | jq -r .version)" >> $GITHUB_ENV
      - name: Set GitHub Release Note
        id: release_note
        uses: actions/github-script@v6
        with:
          script: |
            const result = await exec.getExecOutput(`gh api "/repos/{owner}/{repo}/releases/generate-notes" -f tag_name="v${process.env.PACKAGE_VERSION}" --jq .body`, [], {
              ignoreReturnCode: true,
            })
            core.setOutput('stdout', result.stdout)
        env:
          PACKAGE_VERSION: ${{ env.PACKAGE_VERSION }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Create Pull Request
        id: cpr
        uses: peter-evans/create-pull-request@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "chore(release): v${{ env.PACKAGE_VERSION }}"
          committer: GitHub <<EMAIL>>
          author: ${{ github.actor }} <${{ github.actor }}@users.noreply.github.com>
          assignees: ${{ github.actor }}
          signoff: false
          branch: release/${{ env.PACKAGE_VERSION }}
          branch-suffix: timestamp
          delete-branch: true
          title: "v${{ env.PACKAGE_VERSION }}"
          body: |
            ${{ steps.release_note.outputs.stdout }}
          labels: "Type: Release"
      - name: Check Pull Request
        run: |
          echo "Pull Request Number - ${{ steps.cpr.outputs.pull-request-number }}"
          echo "Pull Request URL - ${{ steps.cpr.outputs.pull-request-url }}"
