const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const path = require('path');
const commonConfig = require('./webpack.config.common.js');

module.exports = merge(commonConfig, {
  mode: 'development',
  devServer: {
    historyApiFallback: {
      rewrites: [
        { from: /^\/$/, to: '/qr-pix-integrated.html' }
      ],
    },
    static: [
      {
        directory: path.join(__dirname, 'public'),
      },
    ],
  },
  devtool: "inline-source-map",
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/qr-pix-integrated.html',
      filename: 'index.html',
      inject: 'head',
      scriptLoading: 'blocking',
    })
  ]
});
